import type { Provider } from '../types'

// 格式化余额显示
export const formatBalance = (balance: number, currency: string): string => {
  switch (currency) {
    case 'USD':
      return `$${balance.toFixed(2)}`
    case 'CNY':
      return `￥${balance.toFixed(2)}`
    case 'TOKEN':
      return `T ${balance.toFixed(0)}`
    default:
      return `$${balance.toFixed(2)}`
  }
}

// 获取提供商信息
export const getProviderInfo = (providers: Provider[], providerCode: string) => {
  return providers.find(p => p.code === providerCode)
}

// 获取提供商颜色
export const getProviderColor = (providers: Provider[], providerCode: string): string => {
  const providerInfo = getProviderInfo(providers, providerCode)
  return providerInfo?.color || 'default'
}

// 获取提供商货币
export const getProviderCurrency = (providers: Provider[], providerCode: string): string => {
  const providerInfo = getProviderInfo(providers, providerCode)
  return providerInfo?.currency || 'USD'
}

// 通用的Set状态更新函数
export const updateSetState = <T>(
  setter: React.Dispatch<React.SetStateAction<Set<T>>>, 
  item: T, 
  action: 'add' | 'delete'
) => {
  setter(prev => {
    const newSet = new Set(prev)
    if (action === 'add') {
      newSet.add(item)
    } else {
      newSet.delete(item)
    }
    return newSet
  })
}

// 格式化时间戳
export const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}

// 搜索过滤函数
export const filterApiKeys = <T extends { name: string; api_key: string }>(
  items: T[],
  searchText: string
): T[] => {
  if (!searchText) return items
  const searchLower = searchText.toLowerCase()
  return items.filter(item =>
    item.name.toLowerCase().includes(searchLower) ||
    item.api_key.toLowerCase().includes(searchLower)
  )
}

// 错误处理工具函数
export const getErrorMessage = (error: any): string => {
  return error.response?.data?.error || error.message || '操作失败'
}

// 延迟执行工具函数
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}
