import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout, App as AntdApp } from 'antd'
import Sidebar from './components/Sidebar'
import TopNavbar from './components/TopNavbar'
import Dashboard from './pages/Dashboard'
import APIKeys from './pages/APIKeys'
import { useResponsive } from './hooks/useResponsive'
import { BREAKPOINTS } from './constants'
import './App.css'

const { Content, Header } = Layout

function App() {
  const isMobile = useResponsive(BREAKPOINTS.MOBILE)

  // 路由配置
  const routes = (
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/apikeys" element={<APIKeys />} />
    </Routes>
  )

  return (
    <AntdApp>
      <Layout style={{ minHeight: '100vh' }}>
        {isMobile ? (
          // 移动端布局：上边栏
          <>
            <Header style={{ padding: 0, background: '#001529', height: '48px', lineHeight: '48px' }}>
              <TopNavbar />
            </Header>
            <Content style={{ margin: '16px', padding: 16, background: '#fff' }}>
              {routes}
            </Content>
          </>
        ) : (
          // 桌面端布局：左边栏
          <>
            <Sidebar />
            <Layout>
              <Content style={{ margin: '16px', padding: 16, background: '#fff' }}>
                {routes}
              </Content>
            </Layout>
          </>
        )}
      </Layout>
    </AntdApp>
  )
}

export default App
