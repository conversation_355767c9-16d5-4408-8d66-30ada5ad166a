# Test unified balance query interface
Write-Host "Testing unified balance query interface..." -ForegroundColor Green

$baseUrl = "http://localhost:8080/api/v1"

# Get first API Key
Write-Host "Getting API Key list..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/apikeys" -Method GET
    $apiKeys = $response
    
    if ($apiKeys.Count -eq 0) {
        Write-Host "No API Keys found. Please add some API Keys first." -ForegroundColor Red
        exit 1
    }
    
    $testApiKeyId = $apiKeys[0].id
    Write-Host "Using API Key ID: $testApiKeyId for testing" -ForegroundColor Cyan
} catch {
    Write-Host "Failed to get API Key list: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 1: Single balance query using unified interface
Write-Host "Test 1: Single balance query using unified interface..." -ForegroundColor Yellow

$singlePayload = @{
    api_key_id = $testApiKeyId
} | ConvertTo-<PERSON><PERSON>

try {
    $singleResponse = Invoke-RestMethod -Uri "$baseUrl/balance" -Method POST -Body $singlePayload -ContentType "application/json"
    Write-Host "Single query response: $($singleResponse.message)" -ForegroundColor Cyan
    Write-Host "Task ID: $($singleResponse.task_id)" -ForegroundColor Cyan
    Write-Host "Count: $($singleResponse.count)" -ForegroundColor Cyan
    
    if ($singleResponse.task_id) {
        Write-Host "SUCCESS: Single balance query works!" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Single balance query may have failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Single balance query failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Batch balance query using unified interface
Write-Host "Test 2: Batch balance query using unified interface..." -ForegroundColor Yellow

# Get multiple API Key IDs for batch test
$batchApiKeyIds = @()
for ($i = 0; $i -lt [Math]::Min(3, $apiKeys.Count); $i++) {
    $batchApiKeyIds += $apiKeys[$i].id
}

$batchPayload = @{
    api_key_ids = $batchApiKeyIds
} | ConvertTo-Json

try {
    $batchResponse = Invoke-RestMethod -Uri "$baseUrl/balance" -Method POST -Body $batchPayload -ContentType "application/json"
    Write-Host "Batch response: $($batchResponse.message)" -ForegroundColor Cyan
    Write-Host "Tasks created: $($batchResponse.count)" -ForegroundColor Cyan
    Write-Host "Task IDs: $($batchResponse.task_ids -join ', ')" -ForegroundColor Cyan
    
    if ($batchResponse.task_ids -and $batchResponse.task_ids.Count -gt 0) {
        Write-Host "SUCCESS: Batch balance query works!" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Batch balance query may have failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Batch balance query failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test duplicate prevention for balance queries
Write-Host "Test 3: Testing duplicate prevention for balance queries..." -ForegroundColor Yellow

$results = @()
for ($i = 1; $i -le 3; $i++) {
    Write-Host "Sending balance query request $i..." -ForegroundColor Cyan
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/balance" -Method POST -Body $singlePayload -ContentType "application/json"
        $results += @{
            Success = $true
            TaskId = $response.task_id
            Message = $response.message
        }
    } catch {
        $results += @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
    Start-Sleep -Milliseconds 200
}

# Analyze duplicate prevention results
Write-Host "Analyzing duplicate prevention results:" -ForegroundColor Green
$successCount = ($results | Where-Object { $_.Success }).Count
$taskIds = ($results | Where-Object { $_.Success -and $_.TaskId } | ForEach-Object { $_.TaskId }) | Sort-Object -Unique

Write-Host "Successful requests: $successCount" -ForegroundColor Cyan
Write-Host "Unique task IDs: $($taskIds.Count)" -ForegroundColor Cyan
Write-Host "Task IDs: $($taskIds -join ', ')" -ForegroundColor Cyan

if ($taskIds.Count -eq 1) {
    Write-Host "SUCCESS: Balance duplicate prevention works!" -ForegroundColor Green
} else {
    Write-Host "WARNING: Balance duplicate tasks may have been created" -ForegroundColor Yellow
}

Write-Host "Test completed!" -ForegroundColor Green
