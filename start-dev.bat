@echo off
echo KeyHub 开发环境启动脚本
echo.

echo 检查Go环境...
go version
if %errorlevel% neq 0 (
    echo 错误: 未找到Go环境，请先安装Go 1.24+
    pause
    exit /b 1
)

echo 检查Node.js环境...
node --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js环境，请先安装Node.js
    pause
    exit /b 1
)

echo.
echo 安装依赖...
echo 安装Go依赖...
go mod tidy

echo 安装前端依赖...
cd frontend
npm install
if %errorlevel% neq 0 (
    echo 错误: 前端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo.
echo 创建环境配置文件...
if not exist .env (
    copy .env.example .env
    echo 已创建 .env 文件，请根据需要修改配置
)

echo.
echo 启动开发服务器...
echo 后端服务将在 http://localhost:8888 启动
echo 前端服务将在 http://localhost:3000 启动
echo.
echo 按任意键开始启动服务器...
pause

echo 启动后端服务...
start "KeyHub Backend" cmd /k "go run cmd/server/main.go"

echo 等待后端服务启动...
timeout /t 3 /nobreak > nul

echo 启动前端服务...
start "KeyHub Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo 开发环境已启动！
echo 后端: http://localhost:8888
echo 前端: http://localhost:3000
echo.
echo 按任意键退出...
pause
