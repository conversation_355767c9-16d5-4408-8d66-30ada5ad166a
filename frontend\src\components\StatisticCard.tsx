import React from 'react'
import { Card, Statistic } from 'antd'
import type { StatisticProps } from 'antd'

interface StatisticCardProps extends StatisticProps {
  style?: React.CSSProperties
}

const StatisticCard: React.FC<StatisticCardProps> = ({
  style,
  ...statisticProps
}) => {
  return (
    <Card style={style} styles={{ body: { padding: '16px' } }}>
      <Statistic {...statisticProps} />
    </Card>
  )
}

export default StatisticCard
