import React from 'react'
import { <PERSON>, Row, Col } from 'antd'
import { KeyOutlined, CheckCircleOutlined, ExclamationCircleOutlined, DollarOutlined } from '@ant-design/icons'
import { useResponsive } from '../hooks/useResponsive'
import { BREAKPOINTS } from '../constants'
import StatisticCard from '../components/StatisticCard'

const Dashboard: React.FC = () => {
  const isMobile = useResponsive(BREAKPOINTS.MOBILE)

  // 响应式列配置
  const colSpan = isMobile ? 12 : 6 // 移动端2列，桌面端4列

  return (
    <div>
      <Card
        styles={{
          body: { padding: '12px 16px' }
        }}
        style={{ marginBottom: 16 }}
      >
        <h1 style={{
          margin: 0,
          fontSize: '22px',
          fontWeight: 600
        }}>
          仪表板
        </h1>
        {!isMobile && (
          <p style={{
            margin: '8px 0 0 0',
            color: '#666',
            fontSize: '14px'
          }}>
            API Key管理系统概览
          </p>
        )}
      </Card>

      <Row gutter={12}>
        <Col span={colSpan}>
          <StatisticCard
            title="总API Key数量"
            value={0}
            prefix={<KeyOutlined />}
          />
        </Col>
        <Col span={colSpan}>
          <StatisticCard
            title="活跃API Key"
            value={0}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#3f8600' }}
          />
        </Col>
        <Col span={colSpan}>
          <StatisticCard
            title="异常API Key"
            value={0}
            prefix={<ExclamationCircleOutlined />}
            valueStyle={{ color: '#cf1322' }}
          />
        </Col>
        <Col span={colSpan}>
          <StatisticCard
            title="总余额"
            value={0}
            prefix={<DollarOutlined />}
            suffix="(多币种)"
          />
        </Col>
      </Row>

      <Row gutter={12} style={{ marginTop: 16 }}>
        <Col span={isMobile ? 24 : 8}>
          <StatisticCard
            title="总充值余额"
            value={0}
            prefix={<DollarOutlined />}
            suffix="(多币种)"
            valueStyle={{ color: '#1890ff' }}
            style={{ marginBottom: isMobile ? 8 : 0 }}
          />
        </Col>
        <Col span={isMobile ? 24 : 8}>
          <StatisticCard
            title="总赠送余额"
            value={0}
            prefix={<DollarOutlined />}
            suffix="(多币种)"
            valueStyle={{ color: '#52c41a' }}
            style={{ marginBottom: isMobile ? 8 : 0 }}
          />
        </Col>
        <Col span={isMobile ? 24 : 8}>
          <StatisticCard
            title="今日消耗"
            value={0}
            prefix={<DollarOutlined />}
            suffix="(多币种)"
            valueStyle={{ color: '#faad14' }}
          />
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
