import React from 'react'
import { Card, Row, Col, Statistic } from 'antd'
import { KeyOutlined, CheckCircleOutlined, ExclamationCircleOutlined, DollarOutlined } from '@ant-design/icons'
import { useResponsive } from '../hooks/useResponsive'

const Dashboard: React.FC = () => {
  const isMobile = useResponsive(768)
  const colSpan = isMobile ? 12 : 6

  return (
    <div>
      <Card
        styles={{
          body: { padding: '12px 16px' }
        }}
        style={{ marginBottom: 16 }}
      >
        <h1 style={{
          margin: 0,
          fontSize: '22px',
          fontWeight: 600
        }}>
          仪表板
        </h1>
        {!isMobile && (
          <p style={{
            margin: '8px 0 0 0',
            color: '#666',
            fontSize: '14px'
          }}>
            API Key管理系统概览
          </p>
        )}
      </Card>

      <Row gutter={12}>
        <Col span={colSpan}>
          <Card styles={{ body: { padding: '16px' } }}>
            <Statistic title="总API Key数量" value={0} prefix={<KeyOutlined />} />
          </Card>
        </Col>
        <Col span={colSpan}>
          <Card styles={{ body: { padding: '16px' } }}>
            <Statistic title="活跃API Key" value={0} prefix={<CheckCircleOutlined />} valueStyle={{ color: '#3f8600' }} />
          </Card>
        </Col>
        <Col span={colSpan}>
          <Card styles={{ body: { padding: '16px' } }}>
            <Statistic title="异常API Key" value={0} prefix={<ExclamationCircleOutlined />} valueStyle={{ color: '#cf1322' }} />
          </Card>
        </Col>
        <Col span={colSpan}>
          <Card styles={{ body: { padding: '16px' } }}>
            <Statistic title="总余额" value={0} prefix={<DollarOutlined />} suffix="(多币种)" />
          </Card>
        </Col>
      </Row>

      <Row gutter={12} style={{ marginTop: 16 }}>
        <Col span={isMobile ? 24 : 8}>
          <Card styles={{ body: { padding: '16px' } }} style={{ marginBottom: isMobile ? 8 : 0 }}>
            <Statistic title="总充值余额" value={0} prefix={<DollarOutlined />} suffix="(多币种)" valueStyle={{ color: '#1890ff' }} />
          </Card>
        </Col>
        <Col span={isMobile ? 24 : 8}>
          <Card styles={{ body: { padding: '16px' } }} style={{ marginBottom: isMobile ? 8 : 0 }}>
            <Statistic title="总赠送余额" value={0} prefix={<DollarOutlined />} suffix="(多币种)" valueStyle={{ color: '#52c41a' }} />
          </Card>
        </Col>
        <Col span={isMobile ? 24 : 8}>
          <Card styles={{ body: { padding: '16px' } }}>
            <Statistic title="今日消耗" value={0} prefix={<DollarOutlined />} suffix="(多币种)" valueStyle={{ color: '#faad14' }} />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
