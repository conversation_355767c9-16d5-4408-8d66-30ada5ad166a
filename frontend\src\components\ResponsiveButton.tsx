import React from 'react'
import { But<PERSON> } from 'antd'
import type { ButtonProps } from 'antd'

interface CompactButtonProps extends ButtonProps {
  isMobile: boolean
  mobileText?: string
  desktopText: string
}

const CompactButton: React.FC<CompactButtonProps> = ({
  isMobile,
  mobileText = '',
  desktopText,
  ...props
}) => {
  return (
    <Button
      {...props}
      size="small"
    >
      {isMobile ? mobileText : desktopText}
    </Button>
  )
}

export default CompactButton
