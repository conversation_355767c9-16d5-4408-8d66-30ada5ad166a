// 响应式断点
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 992,
  DESKTOP: 1200,
} as const

// 延迟时间
export const DELAYS = {
  REFRESH_AFTER_TASK: 3000, // 任务提交后刷新数据的延迟
  BALANCE_REFRESH: 4000,    // 批量余额查询后刷新的延迟
} as const

// 表格配置
export const TABLE_CONFIG = {
  SCROLL_WIDTH: 1200,
  DEFAULT_PAGE_SIZE: 10,
} as const

// 默认隐藏的列
export const DEFAULT_HIDDEN_COLUMNS = ['paid_balance', 'grant_balance', 'created_at'] as const

// 消息文本
export const MESSAGES = {
  NO_API_KEYS_TO_TEST: '没有可测试的API Key',
  SELECT_API_KEYS_FOR_BALANCE: '请先选择要查询余额的API Key',
  SELECT_API_KEYS_FOR_DELETE: '请先选择要删除的API Key',
  FETCH_API_KEYS_FAILED: '获取API Key列表失败',
  FETCH_PROVIDERS_FAILED: '获取提供商列表失败',
  API_KEY_CREATED: 'API Key创建成功',
  API_KEY_UPDATED: 'API Key更新成功',
  API_KEY_DELETED: 'API Key删除成功',
  OPERATION_FAILED: '操作失败',
  DELETE_FAILED: '删除失败',
} as const

// 确认对话框文本
export const CONFIRM_MESSAGES = {
  BATCH_TEST: (count: number) => 
    `批量测试确认\n\n确定要测试所有 ${count} 个API Key吗？\n\n这将对所有API Key进行连通性测试。`,
  BATCH_DELETE: (count: number) => 
    `删除确认\n\n确定要删除选中的 ${count} 个API Key吗？\n\n此操作不可恢复，请谨慎操作。`,
  DELETE_SINGLE: '确定要删除这个API Key吗？',
} as const
