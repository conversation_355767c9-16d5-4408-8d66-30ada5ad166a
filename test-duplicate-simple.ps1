# Test duplicate task prevention
Write-Host "Testing duplicate task prevention..." -ForegroundColor Green

$baseUrl = "http://localhost:8888/api/v1"

# Get first API Key
Write-Host "Getting API Key list..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/apikeys" -Method GET
    $apiKeys = $response
    
    if ($apiKeys.Count -eq 0) {
        Write-Host "No API Keys found. Please add some API Keys first." -ForegroundColor Red
        exit 1
    }
    
    $testApiKeyId = $apiKeys[0].id
    Write-Host "Using API Key ID: $testApiKeyId for testing" -ForegroundColor Cyan
} catch {
    Write-Host "Failed to get API Key list: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 1: Send multiple test requests quickly
Write-Host "Test 1: Sending multiple test requests quickly..." -ForegroundColor Yellow

$testPayload = @{
    api_key_id = $testApiKeyId
    test_type = "chat"
} | ConvertTo-Json

$results = @()
for ($i = 1; $i -le 3; $i++) {
    Write-Host "Sending request $i..." -ForegroundColor Cyan
    try {
        $response = Invoke-RestMethod -Uri "$baseUrl/tests" -Method POST -Body $testPayload -ContentType "application/json"
        $results += @{
            Success = $true
            TaskId = $response.task_id
            Message = $response.message
        }
    } catch {
        $results += @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
    Start-Sleep -Milliseconds 200
}

# Analyze results
Write-Host "Analyzing results:" -ForegroundColor Green
$successCount = ($results | Where-Object { $_.Success }).Count
$taskIds = ($results | Where-Object { $_.Success -and $_.TaskId } | ForEach-Object { $_.TaskId }) | Sort-Object -Unique

Write-Host "Successful requests: $successCount" -ForegroundColor Cyan
Write-Host "Unique task IDs: $($taskIds.Count)" -ForegroundColor Cyan
Write-Host "Task IDs: $($taskIds -join ', ')" -ForegroundColor Cyan

if ($taskIds.Count -eq 1) {
    Write-Host "SUCCESS: Duplicate task prevention works! Multiple requests returned same task ID" -ForegroundColor Green
} else {
    Write-Host "WARNING: Duplicate tasks may have been created" -ForegroundColor Yellow
}

# Test 2: Batch test with duplicate IDs
Write-Host "Test 2: Batch test with duplicate API Key IDs..." -ForegroundColor Yellow

$batchPayload = @{
    api_key_ids = @($testApiKeyId, $testApiKeyId, $testApiKeyId)
    test_type = "chat"
} | ConvertTo-Json

try {
    $batchResponse = Invoke-RestMethod -Uri "$baseUrl/tests" -Method POST -Body $batchPayload -ContentType "application/json"
    Write-Host "Batch response: $($batchResponse.message)" -ForegroundColor Cyan
    Write-Host "Tasks created: $($batchResponse.count)" -ForegroundColor Cyan
    
    $uniqueBatchTaskIds = $batchResponse.task_ids | Sort-Object -Unique
    if ($uniqueBatchTaskIds.Count -eq 1) {
        Write-Host "SUCCESS: Batch duplicate prevention works!" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Batch may have created duplicate tasks" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Batch test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed!" -ForegroundColor Green
